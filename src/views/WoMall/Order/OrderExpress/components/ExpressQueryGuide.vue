<template>
  <section class="express-content express-content--guide">
    <div class="express-guide">
      <p class="express-guide__text">
        您可通过复制物流单号，前往物流公司官网查询物流情况，也可快速访问"快递100"进行查询。快递100：
        <a href="https://www.kuaidi100.com" target="_blank" rel="noopener"
          class="express-guide__link">https://www.kuaidi100.com</a>
      </p>
      <p class="express-guide__note">(建议前往官方网站查询，当查询失效时，可检查单号是否填写正确)</p>
    </div>
  </section>
</template>

<script setup>
</script>

<style lang="less" scoped>
.express-content {
  padding: 15px 15px 15px 56px;
  width: 100%;
  background: @bg-color-white;
  box-sizing: border-box;
  contain: layout style;

  &--guide {
    padding: 10px 20px;
  }
}

.express-guide {
  position: relative;
  padding-bottom: 22px;
  font-size: @font-size-14;
  line-height: @line-height-20;
  text-align: left;
  color: @text-color-primary;

  &::before {
    content: '';
    position: absolute;
    z-index: 2;
    left: -32px;
    display: block;
    width: 20px;
    height: 20px;
    background-size: 100% 100%;
  }

  &__text {
    margin-bottom: 10px;
    line-height: 1.5;
  }

  &__note {
    line-height: 1.3;
    font-size: @font-size-13;
    color: @text-color-tertiary;
  }

  &__link {
    text-decoration: underline;
    color: @text-color-primary;

    &:visited,
    &:active {
      color: @text-color-primary;
    }

    &:hover {
      color: @color-orange;
    }
  }
}
</style>

<template>
  <section class="express-content">
    <ol class="express-timeline">
      <li class="express-item express-item--skeleton" v-for="n in 3" :key="n">
        <div class="express-skeleton express-skeleton--text"></div>
        <div class="express-skeleton express-skeleton--time"></div>
      </li>
    </ol>
  </section>
</template>

<script setup>
</script>

<style lang="less" scoped>
.express-content {
  padding: 15px 15px 15px 56px;
  width: 100%;
  background: @bg-color-white;
  box-sizing: border-box;
  contain: layout style;
}

.express-timeline {
  margin: 0;
  padding: 0;
  list-style: none;
  transform: translateZ(0);
}

.express-item {
  position: relative;
  padding-bottom: 22px;
  font-size: @font-size-14;
  line-height: @line-height-20;
  text-align: left;
  color: @text-color-tertiary;

  &::before {
    content: '';
    position: absolute;
    z-index: 2;
    left: -32px;
    display: block;
    width: 8px;
    height: 8px;
    background-color: @text-color-disabled;
    border: 2px solid @bg-color-white;
    border-radius: 50%;
  }

  &::after {
    content: '';
    position: absolute;
    z-index: 1;
    left: -26px;
    top: 0;
    display: block;
    width: 1px;
    height: 100%;
    background: @divider-color-base;
  }

  &:last-child::after {
    display: none;
  }

  &--skeleton {
    &::before {
      background-color: @text-color-disabled;
      animation: skeleton-loading 1.5s infinite;
    }
  }
}

.express-skeleton {
  background: linear-gradient(90deg, @bg-color-gray 25%, @text-color-disabled 50%, @bg-color-gray 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: @radius-4;

  &--text {
    height: 20px;
    width: 85%;
    margin-bottom: 4px;

    &:nth-child(odd) {
      width: 75%;
    }
  }

  &--time {
    height: 12px;
    width: 120px;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}
</style>

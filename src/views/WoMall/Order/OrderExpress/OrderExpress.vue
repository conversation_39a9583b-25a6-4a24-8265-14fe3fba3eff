<template>
  <main class="express">
    <header class="express__header">
      <dl class="express__info">
        <div class="express__item">
          <dt class="express__label">订单号：</dt>
          <dd class="express__value">{{ orderId }}</dd>
        </div>
        <div class="express__item">
          <dt class="express__label">承运商：</dt>
          <dd class="express__value">{{ displayData.expressName }}</dd>
        </div>
        <div class="express__item">
          <dt class="express__label">运单号：</dt>
          <dd class="express__value">
            <span class="express__number">{{ displayData.expressNo }}</span>
            <button v-if="shouldShowCopyButton" class="express__copy-btn" @click="copyExpressNo"
              aria-label="复制运单号">
              复制
            </button>
          </dd>
        </div>
      </dl>
    </header>

    <section class="express__content" v-if="orderTrackStatus === 1">
      <ol class="express__timeline">
        <li class="express__track-item" v-for="(item, index) in orderTrack"
          :key="`track-${index}-${item.msgTime || item.time}`">
          <div class="express__track-text">{{ item.content || item.context }}</div>
          <time class="express__track-time">{{ item.msgTime || item.time }}</time>
        </li>
      </ol>
    </section>

    <ExpressQueryGuide v-else-if="orderTrackStatus === 2" />

    <ExpressEmptyState v-else-if="orderTrackStatus === 0" />

    <ExpressSkeleton v-else />
  </main>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { showLoadingToast, closeToast, showToast } from 'vant'
import { getExpress } from '@/api/interface/order'
import useClipboard from 'vue-clipboard3'
import { isEmpty, get, defaults, memoize } from 'lodash-es'
import ExpressSkeleton from './components/ExpressSkeleton.vue'
import ExpressEmptyState from './components/ExpressEmptyState.vue'
import ExpressQueryGuide from './components/ExpressQueryGuide.vue'

const orderId = ref('')
const expressName = ref('--')
const expressNo = ref('--')
const orderTrack = ref([])
const orderTrackStatus = ref(-1)

const route = useRoute()
const { toClipboard } = useClipboard()

const shouldShowCopyButton = computed(() =>
  !isEmpty(expressNo.value) && expressNo.value !== '--'
)

const routeParams = computed(() =>
  defaults(route.query, {
    orderId: '',
    supplierSubOrderId: '',
    expressNo: ''
  })
)

const displayData = computed(() => ({
  expressName: expressName.value || '--',
  expressNo: expressNo.value || '--'
}))

const memoizedGetExpressData = memoize(async (orderIdParam, expressNoParam) => {
  try {
    const [err, json] = await getExpress(orderIdParam, expressNoParam)
    return err ? {} : json
  } catch {
    return {}
  }
})

const updateExpressInfo = (deliverInfo) => {
  const safeInfo = defaults(deliverInfo, {
    expressName: '--',
    expressNo: '--',
    orderTrack: []
  })

  expressName.value = safeInfo.expressName
  expressNo.value = safeInfo.expressNo
  orderTrack.value = safeInfo.orderTrack

  if (!isEmpty(deliverInfo) && safeInfo.orderTrack?.length > 0) {
    orderTrackStatus.value = 1
  } else if (!isEmpty(deliverInfo) && (safeInfo.expressName !== '--' || safeInfo.expressNo !== '--')) {
    orderTrackStatus.value = 2
  } else {
    orderTrackStatus.value = 0
  }
}

const copyExpressNo = async () => {
  try {
    await toClipboard(expressNo.value)
    showToast('复制成功')
  } catch {
    showToast('复制失败')
  }
}

onMounted(async () => {
  orderId.value = get(routeParams.value, 'orderId', '')

  try {
    showLoadingToast()
    const { supplierSubOrderId, expressNo: expressNoParam } = routeParams.value
    const deliverInfo = await memoizedGetExpressData(supplierSubOrderId, expressNoParam)
    updateExpressInfo(deliverInfo)
  } catch {
    orderTrackStatus.value = 0
  } finally {
    closeToast()
  }
})

onUnmounted(() => {
  closeToast()
})
</script>

<style lang="less" scoped>
.express {
  width: 100%;
  min-height: 100vh;
  background: @bg-color-white;
  will-change: transform;

  &__header {
    contain: layout style;
    padding: 10px;
    width: 100%;
    margin-bottom: 10px;
    line-height: 15px;
    border-bottom: 9px solid @bg-color-gray;
    font-size: @font-size-13;
    color: @text-color-primary;
    box-sizing: border-box;
  }

  &__info {
    margin: 0;
  }

  &__item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    line-height: 1.5;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__label {
    margin: 0;
    color: @text-color-primary;
    font-weight: @font-weight-400;
  }

  &__value {
    margin: 0;
    color: @text-color-primary;
    display: flex;
    align-items: center;
  }

  &__number {
    margin-right: 10px;
  }

  &__copy-btn {
    margin-left: 10px;
    padding: 0;
    width: 48px;
    height: 23px;
    line-height: 23px;
    border: 1px solid @color-orange;
    border-radius: @radius-2;
    background: transparent;
    font-size: @font-size-13;
    text-align: center;
    color: @color-orange;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  &__content {
    padding: 15px 15px 15px 56px;
    width: 100%;
    background: @bg-color-white;
    box-sizing: border-box;
    contain: layout style;
  }

  &__timeline {
    margin: 0;
    padding: 0;
    list-style: none;
    transform: translateZ(0);
  }

  &__track-item {
    position: relative;
    padding-bottom: 22px;
    font-size: @font-size-14;
    line-height: @line-height-20;
    text-align: left;
    color: @text-color-tertiary;

    &::before {
      content: '';
      position: absolute;
      z-index: 2;
      left: -32px;
      display: block;
      width: 8px;
      height: 8px;
      background-color: @text-color-disabled;
      border: 2px solid @bg-color-white;
      border-radius: 50%;
    }

    &::after {
      content: '';
      position: absolute;
      z-index: 1;
      left: -26px;
      top: 0;
      display: block;
      width: 1px;
      height: 100%;
      background: @divider-color-base;
    }

    &:first-child {
      color: @text-color-primary;

      &::before {
        left: -34px;
        width: 12px;
        height: 12px;
        background-color: @color-orange;
        border: 3px solid @bg-color-white;
      }
    }

    &:last-child::after {
      display: none;
    }
  }

  &__track-text {
    display: block;
    margin-bottom: 4px;
  }

  &__track-time {
    display: block;
    color: @text-color-tertiary;
    font-size: @font-size-12;
  }

}
</style>
